# Trusty TEE GP对象API设计文档

## 1. 概述

本文档描述了Trusty TEE中GP标准对象API的分层架构设计，包括GP API层、中间适配层和底层tee_obj管理层。设计采用清晰的分层结构，确保各层职责明确，便于实现和维护。

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────────────────┐
│                         GP Standard API Layer                           │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │
│  │ Object Lifecycle│  │ Attribute Mgmt  │  │ Object Ops      │         │
│  │                 │  │                 │  │                 │         │
│  │ TEE_Allocate    │  │ TEE_Populate    │  │ TEE_GenerateKey │         │
│  │ TEE_Free        │  │ TEE_GetBuffer   │  │ TEE_CopyAttrs   │         │
│  │ TEE_Close       │  │ TEE_GetValue    │  │ TEE_GetInfo     │         │
│  │ TEE_Reset       │  │ TEE_InitRef     │  │                 │         │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘         │
└─────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                       Middle Adaptation Layer                           │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │
│  │ Handle Validate │  │ Attribute Proc  │  │ State Mgmt      │         │
│  │                 │  │                 │  │                 │         │
│  │ validate_object │  │ populate_attrs  │  │ set_object_info │         │
│  │ validate_trans  │  │ extract_attrs   │  │ get_object_info │         │
│  │                 │  │ copy_attrs      │  │ generate_key    │         │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘         │
└─────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                        tee_obj Management Layer                         │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │
│  │ Object Mgmt     │  │ List Mgmt       │  │ Attribute Mgmt  │         │
│  │                 │  │                 │  │                 │         │
│  │ tee_obj_alloc   │  │ tee_obj_add     │  │ tee_obj_attr_*  │         │
│  │ tee_obj_free    │  │ tee_obj_get     │  │ attr_to_binary  │         │
│  │ tee_obj_close   │  │ tee_obj_remove  │  │ attr_from_bin   │         │
│  │ tee_obj_init    │  │ tee_obj_close_all│ │ attr_copy_from  │         │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘         │
└─────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                         Data Structure Layer                            │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │
│  │ Core Structures │  │ Global Manager  │  │ Sync Primitives │         │
│  │                 │  │                 │  │                 │         │
│  │ struct tee_obj  │  │ g_obj_mgr       │  │ mutex_t lock    │         │
│  │ TEE_ObjectInfo  │  │ obj_list        │  │ list protection │         │
│  │ list_node link  │  │ initialized     │  │                 │         │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘         │
└─────────────────────────────────────────────────────────────────────────┘

                    ┌─────────────────────────────────┐
                    │        Persistent Object        │
                    │         (Reserved)              │
                    │                                 │
                    │  ┌─────────────────────────┐    │
                    │  │     TIPC Channel        │    │
                    │  │   to Storage TA         │    │
                    │  └─────────────────────────┘    │
                    └─────────────────────────────────┘
```

### 2.2 Trusty TEE特点

- **用户空间实现**：所有层都在libutee库中实现，无系统调用
- **TA自管理**：瞬时对象由TA内部链表管理
- **TIPC通信**：持久对象通过TIPC与存储TA通信
- **分层清晰**：每层职责明确，接口清晰

## 3. 设计原则

- **分层架构**：清晰的三层架构，职责分离
- **GP标准兼容**：严格遵循GP Internal Core API规范
- **适配Trusty**：使用Trusty的数据结构和通信机制
- **预留扩展**：为持久对象预留接口
- **简化管理**：链表结构尽可能简单

## 4. 数据结构层 (Data Structure Layer)

### 4.1 核心对象结构

```c
struct tee_obj {
    struct list_node link;        // Trusty链表节点
    TEE_ObjectInfo info;          // GP标准对象信息
    bool busy;                    // 操作忙标志（预留给密钥操作）
    uint32_t have_attrs;          // 属性位字段
    void *attr;                   // 属性数据指针
    size_t ds_pos;                // 数据流位置
    struct tee_pobj *pobj;        // 持久对象指针(瞬时对象为NULL)
    file_handle_t trusty_fh;      // Trusty文件句柄
};
```

### 4.2 全局管理结构

```c
// 全局对象管理器
static struct {
    struct list_node obj_list;    // 对象链表
    mutex_t lock;                 // 保护锁
    bool initialized;             // 初始化标志
} g_obj_mgr = {
    .obj_list = LIST_INITIAL_VALUE(g_obj_mgr.obj_list),
    .initialized = false
};
```

## 5. tee_obj管理层 (tee_obj Management Layer)

### 5.1 核心管理函数

```c
/**
 * 初始化对象管理系统
 */
TEE_Result tee_obj_init(void);

/**
 * 分配tee_obj结构
 */
struct tee_obj *tee_obj_alloc(void);

/**
 * 释放tee_obj结构
 */
void tee_obj_free(struct tee_obj *obj);

/**
 * 添加对象到链表
 */
TEE_Result tee_obj_add(struct tee_obj *obj);

/**
 * 根据对象ID获取对象（核心查找函数）
 */
TEE_Result tee_obj_get(vaddr_t obj_id, struct tee_obj **obj);

/**
 * 关闭对象
 */
void tee_obj_close(struct tee_obj *obj);

/**
 * 关闭所有对象
 */
void tee_obj_close_all(void);

/**
 * 设置对象类型
 */
TEE_Result tee_obj_set_type(struct tee_obj *obj, uint32_t obj_type, size_t max_size);
```

### 5.2 属性管理函数

```c
/**
 * 释放对象属性
 */
void tee_obj_attr_free(struct tee_obj *obj);

/**
 * 清空对象属性
 */
void tee_obj_attr_clear(struct tee_obj *obj);

/**
 * 属性序列化
 */
TEE_Result tee_obj_attr_to_binary(struct tee_obj *obj, void *data, size_t *data_len);

/**
 * 属性反序列化
 */
TEE_Result tee_obj_attr_from_binary(struct tee_obj *obj, const void *data, size_t data_len);

/**
 * 属性复制
 */
TEE_Result tee_obj_attr_copy_from(struct tee_obj *dst, const struct tee_obj *src);
```

### 5.3 设计要点

- **TA自管理**：链表由TA内部管理，不依赖外部上下文
- **obj_id查找**：核心功能是通过obj_id查找obj
- **持久对象预留**：在tee_obj_get中为持久对象预留查找位置
- **链表简化**：使用单一全局链表，管理逻辑清晰

## 6. 中间适配层 (Middle Adaptation Layer)

### 6.1 对象验证函数

```c
/**
 * 验证对象句柄
 */
static TEE_Result validate_object_handle(TEE_ObjectHandle object,
                                         struct tee_obj **obj_out);

/**
 * 验证瞬时对象
 */
static TEE_Result validate_transient_object(TEE_ObjectHandle object,
                                            struct tee_obj **obj_out);
```

### 6.2 对象信息管理

```c
/**
 * 获取对象信息
 */
static TEE_Result get_object_info(struct tee_obj *obj,
                                  TEE_ObjectInfo *info);

/**
 * 设置对象信息
 */
static TEE_Result set_object_info(struct tee_obj *obj,
                                  TEE_ObjectType objectType,
                                  uint32_t maxObjectSize);
```

### 6.3 属性管理

```c
/**
 * 填充对象属性
 */
static TEE_Result populate_object_attributes(struct tee_obj *obj,
                                             const TEE_Attribute *attrs,
                                             uint32_t attrCount);

/**
 * 提取对象属性
 */
static TEE_Result extract_attribute_from_object(struct tee_obj *obj,
                                                uint32_t attributeID,
                                                void *buffer,
                                                size_t *size);

/**
 * 复制对象属性
 */
static TEE_Result copy_object_attributes(struct tee_obj *dst,
                                         const struct tee_obj *src);
```

### 6.4 密钥生成

```c
/**
 * 密钥生成内部实现
 */
static TEE_Result generate_key_internal(struct tee_obj *obj,
                                        uint32_t keySize,
                                        const TEE_Attribute *params,
                                        uint32_t paramCount);
```

### 6.5 设计要点

- **接口适配**：将GP API转换为tee_obj操作
- **参数验证**：验证GP API参数的有效性
- **错误转换**：将底层错误转换为GP标准错误码
- **状态管理**：管理对象的初始化状态和标志

## 7. GP标准API层 (GP Standard API Layer)

### 7.1 对象生命周期管理

```c
/**
 * 分配瞬时对象
 */
TEE_Result TEE_AllocateTransientObject(TEE_ObjectType objectType,
                                       uint32_t maxObjectSize,
                                       TEE_ObjectHandle *object);

/**
 * 释放瞬时对象
 */
void TEE_FreeTransientObject(TEE_ObjectHandle object);

/**
 * 重置瞬时对象
 */
void TEE_ResetTransientObject(TEE_ObjectHandle object);

/**
 * 关闭对象
 */
void TEE_CloseObject(TEE_ObjectHandle object);
```

### 7.2 对象属性管理

```c
/**
 * 填充瞬时对象
 */
TEE_Result TEE_PopulateTransientObject(TEE_ObjectHandle object,
                                       const TEE_Attribute *attrs,
                                       uint32_t attrCount);

/**
 * 获取对象缓冲区属性
 */
TEE_Result TEE_GetObjectBufferAttribute(TEE_ObjectHandle object,
                                        uint32_t attributeID,
                                        void *buffer,
                                        size_t *size);

/**
 * 获取对象值属性
 */
TEE_Result TEE_GetObjectValueAttribute(TEE_ObjectHandle object,
                                       uint32_t attributeID,
                                       uint32_t *a,
                                       uint32_t *b);
```

### 7.3 属性初始化函数

```c
/**
 * 初始化引用属性
 */
void TEE_InitRefAttribute(TEE_Attribute *attr,
                          uint32_t attributeID,
                          const void *buffer,
                          size_t length);

/**
 * 初始化值属性
 */
void TEE_InitValueAttribute(TEE_Attribute *attr,
                            uint32_t attributeID,
                            uint32_t a,
                            uint32_t b);
```

### 7.4 对象操作函数

```c
/**
 * 复制对象属性
 */
TEE_Result TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject,
                                     TEE_ObjectHandle srcObject);

/**
 * 生成密钥
 */
TEE_Result TEE_GenerateKey(TEE_ObjectHandle object,
                           uint32_t keySize,
                           const TEE_Attribute *params,
                           uint32_t paramCount);

/**
 * 获取对象信息
 */
TEE_Result TEE_GetObjectInfo1(TEE_ObjectHandle object,
                              TEE_ObjectInfo *objectInfo);
```

### 7.5 设计要点

- **GP标准兼容**：严格遵循GP Internal Core API规范
- **参数验证**：验证所有输入参数
- **错误处理**：遵循GP规范的错误处理和Panic机制
- **句柄管理**：对象句柄使用对象指针的地址值

## 8. 持久对象预留设计

### 8.1 在tee_obj_get中的预留

```c
TEE_Result tee_obj_get(vaddr_t obj_id, struct tee_obj **obj) {
    // ... 瞬时对象查找逻辑 ...

    // 预留：在持久对象中查找
    /*
    TEE_Result res = tee_pobj_get(obj_id, obj);
    if (res == TEE_SUCCESS) {
        return TEE_SUCCESS;
    }
    */

    return TEE_ERROR_ITEM_NOT_FOUND;
}
```

### 8.2 在tee_obj_close中的预留

```c
void tee_obj_close(struct tee_obj *obj) {
    // ... 瞬时对象处理逻辑 ...

    // 处理持久对象相关资源（预留）
    if (obj->pobj != NULL) {
        // 预留：调用存储TA的持久对象释放函数
        // tee_pobj_release(obj->pobj);

        // 关闭Trusty文件句柄
        if (obj->trusty_fh) {
            // storage_close_file(obj->trusty_fh);
        }
    }
}
```

### 8.3 TIPC通信预留

```c
// 预留：通过TIPC与存储TA通信的接口
/*
static TEE_Result connect_to_storage_ta(handle_t *handle);
static TEE_Result send_storage_request(handle_t handle, ...);
static TEE_Result receive_storage_response(handle_t handle, ...);
*/
```

## 9. 实现要点

### 9.1 分层职责

- **GP API层**：提供标准GP接口，参数验证，错误处理
- **中间适配层**：接口转换，状态管理，属性处理
- **tee_obj管理层**：对象生命周期，链表管理，属性存储
- **数据结构层**：基础数据结构，内存管理

### 9.2 错误处理

- **参数验证**：在GP API层进行
- **状态检查**：在中间适配层进行
- **资源管理**：在tee_obj管理层进行
- **GP规范**：严格遵循GP错误码和Panic机制

### 9.3 线程安全

- **mutex保护**：保护全局对象链表
- **原子操作**：对象状态的原子性修改
- **锁粒度**：最小化锁的持有时间

## 10. 总结

### 10.1 架构优势

1. **分层清晰**：三层架构，职责分离，易于维护
2. **GP兼容**：严格遵循GP Internal Core API规范
3. **Trusty适配**：充分利用Trusty TEE的用户空间特性
4. **扩展性好**：为持久对象预留了完整的扩展接口
5. **实现简单**：链表结构简单，管理逻辑清晰

### 10.2 关键特性

- **用户空间实现**：所有功能在libutee库中完成
- **TA自管理**：瞬时对象由TA内部链表管理
- **obj_id查找**：核心功能通过obj_id查找obj
- **TIPC预留**：为持久对象预留TIPC通信接口
- **线程安全**：适当的锁保护机制

### 10.3 实现指导

1. **自底向上**：先实现tee_obj管理层，再实现中间适配层，最后实现GP API层
2. **接口清晰**：每层接口明确，避免跨层调用
3. **测试驱动**：每层都应有对应的单元测试
4. **文档同步**：实现过程中及时更新设计文档

这个分层架构设计为Trusty TEE的GP对象API实现提供了清晰的技术路线图。


